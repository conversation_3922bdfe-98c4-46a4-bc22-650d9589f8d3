#!/usr/bin/env python3
"""
Общие утилиты для скриптов tools/.

Устраняет дублирование логики работы с:
- ZIP архивами и извлечением FB2 файлов
- Созданием временных файлов и их очисткой
- Инициализацией проектных компонентов парсинга
- Стандартными паттернами обработки ошибок

Все функции используют только проектные компоненты, никакой самописной логики.
"""

import io
import sys
import zipfile
from pathlib import Path
from typing import IO, Any, Callable

sys.path.insert(0, str(Path(__file__).parent.parent))

# Импорты проектных компонентов
from app.processing.canonical_model import CanonicalBook
from app.processing.error_handler import QuarantineError
from app.processing.fragment_detector import FragmentDetector
from app.processing.parser_dispatcher import ParserDispatcher


class ToolsComponents:
    """Синглтон для переиспользования проектных компонентов."""

    _instance: "ToolsComponents | None" = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not hasattr(self, "_initialized"):
            self.parser_dispatcher = ParserDispatcher()
            self.fragment_detector = FragmentDetector()
            self._initialized = True


def get_components() -> ToolsComponents:
    """Получить переиспользуемые компоненты проекта."""
    return ToolsComponents()


def get_canonical_book_from_stream(
    stream: IO[bytes], filename: str, parser_dispatcher: ParserDispatcher = None
) -> CanonicalBook:
    """
    Получает CanonicalBook из потока FB2 используя проектную логику парсинга.

    Args:
        stream: Поток с данными FB2
        filename: Имя файла для контекста
        parser_dispatcher: Опциональный парсер (если None, создается новый)

    Returns:
        CanonicalBook объект
    """
    if parser_dispatcher is None:
        parser_dispatcher = get_components().parser_dispatcher

    return parser_dispatcher.parse_to_canonical_from_stream(stream, filename)


def get_canonical_book_from_file(fb2_path: Path, parser_dispatcher: ParserDispatcher = None) -> CanonicalBook:
    """
    Получает CanonicalBook из FB2 файла (обертка для stream-версии).

    Args:
        fb2_path: Путь к FB2 файлу
        parser_dispatcher: Опциональный парсер

    Returns:
        CanonicalBook объект
    """
    if parser_dispatcher is None:
        parser_dispatcher = get_components().parser_dispatcher

    with open(fb2_path, "rb") as f:
        return parser_dispatcher.parse_to_canonical_from_stream(f, fb2_path.name)


def process_zip_archive(
    zip_path: str | Path,
    fb2_processor: Callable[[str, str, io.BytesIO], Any],
    fb2_filter: Callable[[str], bool] = None,
    error_handler: Callable[[str, str, Exception], None] = None,
) -> list[Any]:
    """
    Универсальная функция для обработки ZIP архивов с FB2 файлами в памяти.

    Args:
        zip_path: Путь к ZIP архиву
        fb2_processor: Функция обработки FB2 (archive_path, fb2_filename, fb2_stream) -> result
        fb2_filter: Опциональный фильтр FB2 файлов (fb2_filename) -> bool
        error_handler: Опциональный обработчик ошибок (archive_path, fb2_filename, exception) -> None

    Returns:
        Список результатов обработки FB2 файлов
    """
    results = []
    zip_path_str = str(zip_path)

    try:
        with zipfile.ZipFile(zip_path_str, "r") as zip_ref:
            for file_info in zip_ref.infolist():
                if file_info.is_dir() or not file_info.filename.lower().endswith(".fb2"):
                    continue

                # Применяем фильтр если указан
                if fb2_filter and not fb2_filter(file_info.filename):
                    continue

                try:
                    # Извлекаем FB2 в память
                    with zip_ref.open(file_info) as fb2_file:
                        fb2_stream = io.BytesIO(fb2_file.read())

                    # Обрабатываем файл из памяти
                    result = fb2_processor(zip_path_str, file_info.filename, fb2_stream)
                    if result is not None:
                        results.append(result)

                except QuarantineError:
                    # Тихо пропускаем файлы в карантине
                    pass
                except Exception as e:
                    # Вызываем обработчик ошибок если указан
                    if error_handler:
                        error_handler(zip_path_str, file_info.filename, e)

    except Exception as e:
        # Ошибка самого ZIP архива
        if error_handler:
            error_handler(zip_path_str, "", e)

    return results


def collect_zip_archives(paths: list[str], limit: int = None) -> list[str]:
    """
    Собирает пути ко всем ZIP архивам в указанных директориях.

    Args:
        paths: Список путей для сканирования
        limit: Максимальное количество архивов (None = без лимита)

    Returns:
        Список путей к ZIP архивам
    """
    archive_paths = []

    for scan_path in paths:
        scan_path_obj = Path(scan_path)
        if not scan_path_obj.exists():
            continue

        if scan_path_obj.is_file() and scan_path_obj.suffix.lower() == ".zip":
            archive_paths.append(str(scan_path_obj))
        else:
            # Рекурсивно ищем ZIP файлы
            for zip_file in scan_path_obj.rglob("*.zip"):
                archive_paths.append(str(zip_file))

        # Применяем лимит если указан
        if limit and len(archive_paths) >= limit:
            archive_paths = archive_paths[:limit]
            break

    return archive_paths


def get_fb2_transformer_from_parser(parser_dispatcher: ParserDispatcher):
    """
    Получает FB2Transformer из ParserDispatcher если доступен.

    Args:
        parser_dispatcher: Экземпляр ParserDispatcher

    Returns:
        FB2Transformer или None если недоступен
    """
    if hasattr(parser_dispatcher, "_last_transformer"):
        return parser_dispatcher._last_transformer
    return None


# Вспомогательные функции для стандартных операций
def print_processing_status(file_path: str, status: str, anomaly_types: list[str] = None) -> None:
    """
    Стандартный вывод статуса обработки файла.

    Args:
        file_path: Полный путь к файлу (archive::fb2_filename)
        status: Статус обработки
        anomaly_types: Список обнаруженных аномалий
    """
    anomaly_str = ""
    if anomaly_types:
        anomaly_str = f" ⚠️  ANOMALY: {', '.join(anomaly_types)}"

    print(f"{file_path} - {status}{anomaly_str}")


def format_file_path(archive_path: str, fb2_filename: str) -> str:
    """
    Форматирует путь к файлу для вывода в стандартном формате.

    Args:
        archive_path: Путь к архиву
        fb2_filename: Имя FB2 файла

    Returns:
        Отформатированный путь
    """
    return f"{archive_path}::{fb2_filename}"
